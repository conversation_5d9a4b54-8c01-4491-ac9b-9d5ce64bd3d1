<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DbOrderMapper">
    
    <resultMap type="DbOrder" id="DbOrderResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="time"    column="time"    />
        <result property="price"    column="price"    />
        <result property="name"    column="name"    />
        <result property="company"    column="company"    />
        <result property="country"    column="country"    />
        <result property="city"    column="city"    />
        <result property="steet1"    column="steet1"    />
        <result property="steet2"    column="steet2"    />
        <result property="postal"    column="postal"    />
        <result property="phone"    column="phone"    />
        <result property="emali"    column="emali"    />
        <result property="note"    column="note"    />
    </resultMap>

    <resultMap id="DbOrderDbOrderItemResult" type="DbOrder" extends="DbOrderResult">
        <collection property="dbOrderItemList" ofType="DbOrderItem" column="id" select="selectDbOrderItemList" />
    </resultMap>

    <resultMap type="DbOrderItem" id="DbOrderItemResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="count"    column="count"    />
    </resultMap>

    <sql id="selectDbOrderVo">
        select id, uid, time, price, name, company, country, city, steet1, steet2, postal, phone, emali, note from db_order
    </sql>

    <select id="selectDbOrderList" parameterType="DbOrder" resultMap="DbOrderResult">
        <include refid="selectDbOrderVo"/>
        <where>  
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="time != null "> and time = #{time}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="steet1 != null  and steet1 != ''"> and steet1 = #{steet1}</if>
            <if test="steet2 != null  and steet2 != ''"> and steet2 = #{steet2}</if>
            <if test="postal != null  and postal != ''"> and postal = #{postal}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="emali != null  and emali != ''"> and emali = #{emali}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
        </where>
    </select>
    
    <select id="selectDbOrderById" parameterType="Long" resultMap="DbOrderDbOrderItemResult">
        select id, uid, time, price, name, company, country, city, steet1, steet2, postal, phone, emali, note
        from db_order
        where id = #{id}
    </select>

    <select id="selectDbOrderItemList" resultMap="DbOrderItemResult">
        select id, order_id, title, price, count
        from db_order_item
        where order_id = #{order_id}
    </select>

    <insert id="insertDbOrder" parameterType="DbOrder" useGeneratedKeys="true" keyProperty="id">
        insert into db_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="time != null">time,</if>
            <if test="price != null">price,</if>
            <if test="name != null">name,</if>
            <if test="company != null">company,</if>
            <if test="country != null">country,</if>
            <if test="city != null">city,</if>
            <if test="steet1 != null">steet1,</if>
            <if test="steet2 != null">steet2,</if>
            <if test="postal != null">postal,</if>
            <if test="phone != null">phone,</if>
            <if test="emali != null">emali,</if>
            <if test="note != null">note,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="time != null">#{time},</if>
            <if test="price != null">#{price},</if>
            <if test="name != null">#{name},</if>
            <if test="company != null">#{company},</if>
            <if test="country != null">#{country},</if>
            <if test="city != null">#{city},</if>
            <if test="steet1 != null">#{steet1},</if>
            <if test="steet2 != null">#{steet2},</if>
            <if test="postal != null">#{postal},</if>
            <if test="phone != null">#{phone},</if>
            <if test="emali != null">#{emali},</if>
            <if test="note != null">#{note},</if>
         </trim>
    </insert>

    <update id="updateDbOrder" parameterType="DbOrder">
        update db_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="time != null">time = #{time},</if>
            <if test="price != null">price = #{price},</if>
            <if test="name != null">name = #{name},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="city != null">city = #{city},</if>
            <if test="steet1 != null">steet1 = #{steet1},</if>
            <if test="steet2 != null">steet2 = #{steet2},</if>
            <if test="postal != null">postal = #{postal},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="emali != null">emali = #{emali},</if>
            <if test="note != null">note = #{note},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbOrderById" parameterType="Long">
        delete from db_order where id = #{id}
    </delete>

    <delete id="deleteDbOrderByIds" parameterType="String">
        delete from db_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteDbOrderItemByOrderIds" parameterType="String">
        delete from db_order_item where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <delete id="deleteDbOrderItemByOrderId" parameterType="Long">
        delete from db_order_item where order_id = #{orderId}
    </delete>

    <insert id="batchDbOrderItem">
        insert into db_order_item( id, order_id, title, price, count) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.orderId}, #{item.title}, #{item.price}, #{item.count})
        </foreach>
    </insert>
</mapper>