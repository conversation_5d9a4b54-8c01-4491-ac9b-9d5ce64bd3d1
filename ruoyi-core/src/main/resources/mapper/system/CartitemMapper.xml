<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CartitemMapper">
    
    <resultMap type="Cartitem" id="CartitemResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="count"    column="count"    />
    </resultMap>

    <sql id="selectCartitemVo">
        select id, uid, title, price, count from db_cart_item
    </sql>

    <select id="selectCartitemList" parameterType="Cartitem" resultMap="CartitemResult">
        <include refid="selectCartitemVo"/>
        <where>  
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>
    
    <select id="selectCartitemById" parameterType="Long" resultMap="CartitemResult">
        <include refid="selectCartitemVo"/>
        where id = #{id}
    </select>

    <insert id="insertCartitem" parameterType="Cartitem" useGeneratedKeys="true" keyProperty="id">
        insert into db_cart_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="title != null">title,</if>
            <if test="price != null">price,</if>
            <if test="count != null">count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="title != null">#{title},</if>
            <if test="price != null">#{price},</if>
            <if test="count != null">#{count},</if>
         </trim>
    </insert>

    <update id="updateCartitem" parameterType="Cartitem">
        update db_cart_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="title != null">title = #{title},</if>
            <if test="price != null">price = #{price},</if>
            <if test="count != null">count = #{count},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCartitemById" parameterType="Long">
        delete from db_cart_item where id = #{id}
    </delete>

    <delete id="deleteCartitemByIds" parameterType="String">
        delete from db_cart_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>