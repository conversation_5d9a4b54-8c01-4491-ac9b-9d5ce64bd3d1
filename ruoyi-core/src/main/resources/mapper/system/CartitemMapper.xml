<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CartItemMapper">
    
    <resultMap type="CartItem" id="CartItemResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="count"    column="count"    />
        <result property="courseId"    column="course_id"    />
    </resultMap>

    <sql id="selectCartItemVo">
        select id, uid, title, price, count, course_id from db_cart_item
    </sql>

    <select id="selectCartItemList" parameterType="CartItem" resultMap="CartItemResult">
        <include refid="selectCartItemVo"/>
        <where>  
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="count != null "> and count = #{count}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
        </where>
    </select>
    
    <select id="selectCartItemById" parameterType="Long" resultMap="CartItemResult">
        <include refid="selectCartItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertCartItem" parameterType="CartItem" useGeneratedKeys="true" keyProperty="id">
        insert into db_cart_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="title != null">title,</if>
            <if test="price != null">price,</if>
            <if test="count != null">count,</if>
            <if test="courseId != null">course_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="title != null">#{title},</if>
            <if test="price != null">#{price},</if>
            <if test="count != null">#{count},</if>
            <if test="courseId != null">#{courseId},</if>
         </trim>
    </insert>

    <update id="updateCartItem" parameterType="CartItem">
        update db_cart_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="title != null">title = #{title},</if>
            <if test="price != null">price = #{price},</if>
            <if test="count != null">count = #{count},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCartItemById" parameterType="Long">
        delete from db_cart_item where id = #{id}
    </delete>

    <delete id="deleteCartItemByIds" parameterType="String">
        delete from db_cart_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>