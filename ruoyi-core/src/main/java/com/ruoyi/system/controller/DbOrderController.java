package com.ruoyi.system.controller;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.CartItem;
import com.ruoyi.system.domain.DbOrderItem;
import com.ruoyi.system.service.ICartItemService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DbOrder;
import com.ruoyi.system.service.IDbOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 订单列表Controller
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/system/order")
public class DbOrderController extends BaseController
{
    @Autowired
    private IDbOrderService dbOrderService;
    @Resource
    private ICartItemService cartItemService;

    /**
     * 查询订单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbOrder dbOrder)
    {
        startPage();
        List<DbOrder> list = dbOrderService.selectDbOrderList(dbOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "订单列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbOrder dbOrder)
    {
        List<DbOrder> list = dbOrderService.selectDbOrderList(dbOrder);
        ExcelUtil<DbOrder> util = new ExcelUtil<DbOrder>(DbOrder.class);
        util.exportExcel(response, list, "订单列表数据");
    }

    /**
     * 获取订单列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dbOrderService.selectDbOrderById(id));
    }

    /**
     * 新增订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "订单列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DbOrder dbOrder)
    {
        return toAjax(dbOrderService.insertDbOrder(dbOrder));
    }

    /**
     * 修改订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DbOrder dbOrder)
    {
        return toAjax(dbOrderService.updateDbOrder(dbOrder));
    }

    /**
     * 删除订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "订单列表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbOrderService.deleteDbOrderByIds(ids));
    }

    @PostMapping("/create")
    public AjaxResult createUserOrder(@RequestBody DbOrder dbOrder){

        List<CartItem> items = cartItemService.selectCartItemListById(SecurityUtils.getUserId());
        // 将购物车商品转换为订单商品
        List<DbOrderItem> dbOrderItems = items.stream().map(item -> {
            DbOrderItem orderItem = new DbOrderItem();
            BeanUtils.copyProperties(item, orderItem, "id");
            return orderItem;
        }).toList();
        // 计算总价
        Long totalPrice = dbOrderItems.stream().
                map(item -> item.getCount() * item.getPrice().longValue())
                .reduce(Long::sum).orElse(0L);

        dbOrder.setUid(SecurityUtils.getUserId());
        dbOrder.setTime(new Date());
        dbOrder.setPrice(totalPrice.doubleValue());
        dbOrder.setDbOrderItemList(dbOrderItems);
        System.out.print(dbOrder);
        return toAjax(dbOrderService.insertDbOrder(dbOrder));
    }
}
