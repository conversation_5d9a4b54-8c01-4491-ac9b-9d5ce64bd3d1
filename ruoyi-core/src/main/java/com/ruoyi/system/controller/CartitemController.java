package com.ruoyi.system.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Cartitem;
import com.ruoyi.system.service.ICartitemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 购物车商品Controller
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/system/item")
public class CartitemController extends BaseController
{
    @Autowired
    private ICartitemService cartitemService;

    /**
     * 查询购物车商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:list')")
    @GetMapping("/list")
    public TableDataInfo list(Cartitem cartitem)
    {
        startPage();
        List<Cartitem> list = cartitemService.selectCartitemList(cartitem);
        return getDataTable(list);
    }

    /**
     * 导出购物车商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:export')")
    @Log(title = "购物车商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Cartitem cartitem)
    {
        List<Cartitem> list = cartitemService.selectCartitemList(cartitem);
        ExcelUtil<Cartitem> util = new ExcelUtil<Cartitem>(Cartitem.class);
        util.exportExcel(response, list, "购物车商品数据");
    }

    /**
     * 获取购物车商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:item:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cartitemService.selectCartitemById(id));
    }

    /**
     * 新增购物车商品
     */
    @PreAuthorize("@ss.hasPermi('system:item:add')")
    @Log(title = "购物车商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Cartitem cartitem)
    {
        return toAjax(cartitemService.insertCartitem(cartitem));
    }

    /**
     * 修改购物车商品
     */
    @PreAuthorize("@ss.hasPermi('system:item:edit')")
    @Log(title = "购物车商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Cartitem cartitem)
    {
        return toAjax(cartitemService.updateCartitem(cartitem));
    }

    /**
     * 删除购物车商品
     */
    @PreAuthorize("@ss.hasPermi('system:item:remove')")
    @Log(title = "购物车商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cartitemService.deleteCartitemByIds(ids));
    }
}
