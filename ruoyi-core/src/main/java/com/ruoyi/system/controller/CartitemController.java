package com.ruoyi.system.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CartItem;
import com.ruoyi.system.service.ICartItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 购物车列Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/system/item")
public class CartItemController extends BaseController
{
    @Autowired
    private ICartItemService cartItemService;

    /**
     * 查询购物车列列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:list')")
    @GetMapping("/list")
    public TableDataInfo list(CartItem cartItem)
    {
        startPage();
        List<CartItem> list = cartItemService.selectCartItemList(cartItem);
        return getDataTable(list);
    }

    /**
     * 导出购物车列列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:export')")
    @Log(title = "购物车列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CartItem cartItem)
    {
        List<CartItem> list = cartItemService.selectCartItemList(cartItem);
        ExcelUtil<CartItem> util = new ExcelUtil<CartItem>(CartItem.class);
        util.exportExcel(response, list, "购物车列数据");
    }

    /**
     * 获取购物车列详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:item:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cartItemService.selectCartItemById(id));
    }

    /**
     * 新增购物车列
     */
    @PreAuthorize("@ss.hasPermi('system:item:add')")
    @Log(title = "购物车列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CartItem cartItem)
    {
        return toAjax(cartItemService.insertCartItem(cartItem));
    }

    /**
     * 修改购物车列
     */
    @PreAuthorize("@ss.hasPermi('system:item:edit')")
    @Log(title = "购物车列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CartItem cartItem)
    {
        return toAjax(cartItemService.updateCartItem(cartItem));
    }

    /**
     * 删除购物车列
     */
    @PreAuthorize("@ss.hasPermi('system:item:remove')")
    @Log(title = "购物车列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cartItemService.deleteCartItemByIds(ids));
    }
}
