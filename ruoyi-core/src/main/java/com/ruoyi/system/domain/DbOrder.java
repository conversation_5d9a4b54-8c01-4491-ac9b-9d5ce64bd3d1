package com.ruoyi.system.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单列表对象 db_order
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public class DbOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单号 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long uid;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date time;

    /** 合计金额 */
    @Excel(name = "合计金额")
    private Double price;

    /** 收件人名称 */
    @Excel(name = "收件人名称")
    private String name;

    /** 软件公司 */
    @Excel(name = "软件公司")
    private String company;

    /** 国家 */
    @Excel(name = "国家")
    private String country;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 地址1 */
    @Excel(name = "地址1")
    private String steet1;

    /** 地址2 */
    @Excel(name = "地址2")
    private String steet2;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    private String postal;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String emali;

    /** 订单备注 */
    @Excel(name = "订单备注")
    private String note;

    /** 订单商品列表信息 */
    private List<DbOrderItem> dbOrderItemList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUid(Long uid) 
    {
        this.uid = uid;
    }

    public Long getUid() 
    {
        return uid;
    }
    public void setTime(Date time) 
    {
        this.time = time;
    }

    public Date getTime() 
    {
        return time;
    }
    public void setPrice(Double price) 
    {
        this.price = price;
    }

    public Double getPrice() 
    {
        return price;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setSteet1(String steet1) 
    {
        this.steet1 = steet1;
    }

    public String getSteet1() 
    {
        return steet1;
    }
    public void setSteet2(String steet2) 
    {
        this.steet2 = steet2;
    }

    public String getSteet2() 
    {
        return steet2;
    }
    public void setPostal(String postal) 
    {
        this.postal = postal;
    }

    public String getPostal() 
    {
        return postal;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setEmali(String emali) 
    {
        this.emali = emali;
    }

    public String getEmali() 
    {
        return emali;
    }
    public void setNote(String note) 
    {
        this.note = note;
    }

    public String getNote() 
    {
        return note;
    }

    public List<DbOrderItem> getDbOrderItemList()
    {
        return dbOrderItemList;
    }

    public void setDbOrderItemList(List<DbOrderItem> dbOrderItemList)
    {
        this.dbOrderItemList = dbOrderItemList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uid", getUid())
            .append("time", getTime())
            .append("price", getPrice())
            .append("name", getName())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("city", getCity())
            .append("steet1", getSteet1())
            .append("steet2", getSteet2())
            .append("postal", getPostal())
            .append("phone", getPhone())
            .append("emali", getEmali())
            .append("note", getNote())
            .append("dbOrderItemList", getDbOrderItemList())
            .toString();
    }
}
