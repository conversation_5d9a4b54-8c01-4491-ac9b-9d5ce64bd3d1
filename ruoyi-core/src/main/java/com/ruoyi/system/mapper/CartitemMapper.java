package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CartItem;

/**
 * 购物车列Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface CartItemMapper 
{
    /**
     * 查询购物车列
     * 
     * @param id 购物车列主键
     * @return 购物车列
     */
    public CartItem selectCartItemById(Long id);

    /**
     * 查询购物车列列表
     * 
     * @param cartItem 购物车列
     * @return 购物车列集合
     */
    public List<CartItem> selectCartItemList(CartItem cartItem);

    /**
     * 新增购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    public int insertCartItem(CartItem cartItem);

    /**
     * 修改购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    public int updateCartItem(CartItem cartItem);

    /**
     * 删除购物车列
     * 
     * @param id 购物车列主键
     * @return 结果
     */
    public int deleteCartItemById(Long id);

    /**
     * 批量删除购物车列
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCartItemByIds(Long[] ids);
}
