package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.DbOrder;
import com.ruoyi.system.domain.DbOrderItem;

/**
 * 订单列表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface DbOrderMapper 
{
    /**
     * 查询订单列表
     * 
     * @param id 订单列表主键
     * @return 订单列表
     */
    public DbOrder selectDbOrderById(Long id);

    /**
     * 查询订单列表列表
     * 
     * @param dbOrder 订单列表
     * @return 订单列表集合
     */
    public List<DbOrder> selectDbOrderList(DbOrder dbOrder);

    /**
     * 新增订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    public int insertDbOrder(DbOrder dbOrder);

    /**
     * 修改订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    public int updateDbOrder(DbOrder dbOrder);

    /**
     * 删除订单列表
     * 
     * @param id 订单列表主键
     * @return 结果
     */
    public int deleteDbOrderById(Long id);

    /**
     * 批量删除订单列表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbOrderByIds(Long[] ids);

    /**
     * 批量删除订单商品列表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbOrderItemByOrderIds(Long[] ids);
    
    /**
     * 批量新增订单商品列表
     * 
     * @param dbOrderItemList 订单商品列表列表
     * @return 结果
     */
    public int batchDbOrderItem(List<DbOrderItem> dbOrderItemList);
    

    /**
     * 通过订单列表主键删除订单商品列表信息
     * 
     * @param id 订单列表ID
     * @return 结果
     */
    public int deleteDbOrderItemByOrderId(Long id);
}
