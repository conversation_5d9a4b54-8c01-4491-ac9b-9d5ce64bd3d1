package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.DbOrderItem;
import com.ruoyi.system.mapper.DbOrderMapper;
import com.ruoyi.system.domain.DbOrder;
import com.ruoyi.system.service.IDbOrderService;

/**
 * 订单列表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class DbOrderServiceImpl implements IDbOrderService 
{
    @Autowired
    private DbOrderMapper dbOrderMapper;

    /**
     * 查询订单列表
     * 
     * @param id 订单列表主键
     * @return 订单列表
     */
    @Override
    public DbOrder selectDbOrderById(Long id)
    {
        return dbOrderMapper.selectDbOrderById(id);
    }

    /**
     * 查询订单列表列表
     * 
     * @param dbOrder 订单列表
     * @return 订单列表
     */
    @Override
    public List<DbOrder> selectDbOrderList(DbOrder dbOrder)
    {
        return dbOrderMapper.selectDbOrderList(dbOrder);
    }

    /**
     * 新增订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    @Transactional
    @Override
    public int insertDbOrder(DbOrder dbOrder)
    {
        int rows = dbOrderMapper.insertDbOrder(dbOrder);
        insertDbOrderItem(dbOrder);
        return rows;
    }

    /**
     * 修改订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    @Transactional
    @Override
    public int updateDbOrder(DbOrder dbOrder)
    {
        dbOrderMapper.deleteDbOrderItemByOrderId(dbOrder.getId());
        insertDbOrderItem(dbOrder);
        return dbOrderMapper.updateDbOrder(dbOrder);
    }

    /**
     * 批量删除订单列表
     * 
     * @param ids 需要删除的订单列表主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteDbOrderByIds(Long[] ids)
    {
        dbOrderMapper.deleteDbOrderItemByOrderIds(ids);
        return dbOrderMapper.deleteDbOrderByIds(ids);
    }

    /**
     * 删除订单列表信息
     * 
     * @param id 订单列表主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteDbOrderById(Long id)
    {
        dbOrderMapper.deleteDbOrderItemByOrderId(id);
        return dbOrderMapper.deleteDbOrderById(id);
    }

    /**
     * 新增订单商品列表信息
     * 
     * @param dbOrder 订单列表对象
     */
    public void insertDbOrderItem(DbOrder dbOrder)
    {
        List<DbOrderItem> dbOrderItemList = dbOrder.getDbOrderItemList();
        Long id = dbOrder.getId();
        if (StringUtils.isNotNull(dbOrderItemList))
        {
            List<DbOrderItem> list = new ArrayList<DbOrderItem>();
            for (DbOrderItem dbOrderItem : dbOrderItemList)
            {
                dbOrderItem.setOrderId(id);
                list.add(dbOrderItem);
            }
            if (list.size() > 0)
            {
                dbOrderMapper.batchDbOrderItem(list);
            }
        }
    }
}
