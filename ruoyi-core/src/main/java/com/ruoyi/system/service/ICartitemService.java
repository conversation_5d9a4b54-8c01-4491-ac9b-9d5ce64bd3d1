package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Cartitem;

/**
 * 购物车商品Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ICartitemService 
{
    /**
     * 查询购物车商品
     * 
     * @param id 购物车商品主键
     * @return 购物车商品
     */
    public Cartitem selectCartitemById(Long id);

    /**
     * 查询购物车商品列表
     * 
     * @param cartitem 购物车商品
     * @return 购物车商品集合
     */
    public List<Cartitem> selectCartitemList(Cartitem cartitem);

    /**
     * 新增购物车商品
     * 
     * @param cartitem 购物车商品
     * @return 结果
     */
    public int insertCartitem(Cartitem cartitem);

    /**
     * 修改购物车商品
     * 
     * @param cartitem 购物车商品
     * @return 结果
     */
    public int updateCartitem(Cartitem cartitem);

    /**
     * 批量删除购物车商品
     * 
     * @param ids 需要删除的购物车商品主键集合
     * @return 结果
     */
    public int deleteCartitemByIds(Long[] ids);

    /**
     * 删除购物车商品信息
     * 
     * @param id 购物车商品主键
     * @return 结果
     */
    public int deleteCartitemById(Long id);
}
