package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.DbOrder;

/**
 * 订单列表Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IDbOrderService 
{
    /**
     * 查询订单列表
     * 
     * @param id 订单列表主键
     * @return 订单列表
     */
    public DbOrder selectDbOrderById(Long id);

    /**
     * 查询订单列表列表
     * 
     * @param dbOrder 订单列表
     * @return 订单列表集合
     */
    public List<DbOrder> selectDbOrderList(DbOrder dbOrder);

    /**
     * 新增订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    public int insertDbOrder(DbOrder dbOrder);

    /**
     * 修改订单列表
     * 
     * @param dbOrder 订单列表
     * @return 结果
     */
    public int updateDbOrder(DbOrder dbOrder);

    /**
     * 批量删除订单列表
     * 
     * @param ids 需要删除的订单列表主键集合
     * @return 结果
     */
    public int deleteDbOrderByIds(Long[] ids);

    /**
     * 删除订单列表信息
     * 
     * @param id 订单列表主键
     * @return 结果
     */
    public int deleteDbOrderById(Long id);
}
