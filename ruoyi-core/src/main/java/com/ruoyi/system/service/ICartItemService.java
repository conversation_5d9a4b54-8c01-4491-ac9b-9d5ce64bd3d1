package com.ruoyi.system.service;

import java.util.List;

import com.ruoyi.system.domain.CartItem;

/**
 * 购物车列Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ICartItemService 
{
    /**
     * 查询购物车列
     * 
     * @param id 购物车列主键
     * @return 购物车列
     */
    public CartItem selectCartItemById(Long id);

    /**
     * 查询购物车列列表
     * 
     * @param cartItem 购物车列
     * @return 购物车列集合
     */
    public List<CartItem> selectCartItemList(CartItem cartItem);

    /**
     * 查询购物车列
     *
     * @param courseId 购物车列主键
     * @param userId 购物车列主键
     * @return 购物车列
     */
    CartItem selectCourseItem(Long courseId, Long userId);

    long selectCount(Long userId);

    /**
     * 新增购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    public int insertCartItem(CartItem cartItem);

    /**
     * 修改购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    public int updateCartItem(CartItem cartItem);

    /**
     * 批量删除购物车列
     * 
     * @param ids 需要删除的购物车列主键集合
     * @return 结果
     */
    public int deleteCartItemByIds(Long[] ids);

    /**
     * 删除购物车列信息
     * 
     * @param id 购物车列主键
     * @return 结果
     */
    public int deleteCartItemById(Long id);

    /**
     * 删除用户购物车购物车列信息
     *
     * @param userId 用户id
     * @return 结果
     */
    int deleteUserCartItemById(Long id, Long userId);

    int increaseUserCartItem(Long id, Long userId);
}
