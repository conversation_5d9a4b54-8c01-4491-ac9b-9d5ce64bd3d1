package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CartItemMapper;
import com.ruoyi.system.domain.CartItem;
import com.ruoyi.system.service.ICartItemService;

/**
 * 购物车列Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class CartItemServiceImpl implements ICartItemService 
{
    @Autowired
    private CartItemMapper cartItemMapper;

    /**
     * 查询购物车列
     * 
     * @param id 购物车列主键
     * @return 购物车列
     */
    @Override
    public CartItem selectCartItemById(Long id)
    {
        return cartItemMapper.selectCartItemById(id);
    }

    @Override
    public CartItem selectCourseItem(Long courseId, Long userId) {
        return cartItemMapper.selectCourseItem(courseId, userId);
    }

    @Override
    public long selectCount(Long userId) {
        List<CartItem> cartItems = cartItemMapper.selectCartItemListById(userId);
        System.out.println(cartItems);
        Optional<Long> reduce = cartItems.stream().map(CartItem::getCount).reduce((Long::sum));
        return reduce.orElse(0L);
    }

    /**
     * 查询购物车列列表
     * 
     * @param cartItem 购物车列
     * @return 购物车列
     */
    @Override
    public List<CartItem> selectCartItemList(CartItem cartItem)
    {
        return cartItemMapper.selectCartItemList(cartItem);
    }

    /**
     * 新增购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    @Override
    public int insertCartItem(CartItem cartItem)
    {
        return cartItemMapper.insertCartItem(cartItem);
    }

    /**
     * 修改购物车列
     * 
     * @param cartItem 购物车列
     * @return 结果
     */
    @Override
    public int updateCartItem(CartItem cartItem)
    {
        return cartItemMapper.updateCartItem(cartItem);
    }

    /**
     * 批量删除购物车列
     * 
     * @param ids 需要删除的购物车列主键
     * @return 结果
     */
    @Override
    public int deleteCartItemByIds(Long[] ids)
    {
        return cartItemMapper.deleteCartItemByIds(ids);
    }

    /**
     * 删除购物车列信息
     * 
     * @param id 购物车列主键
     * @return 结果
     */
    @Override
    public int deleteCartItemById(Long id)
    {
        return cartItemMapper.deleteCartItemById(id);
    }

    @Override
    public int deleteUserCartItemById(Long id, Long userId) {
        return cartItemMapper.deleteUserCartItemById(id, userId);
    }
}
