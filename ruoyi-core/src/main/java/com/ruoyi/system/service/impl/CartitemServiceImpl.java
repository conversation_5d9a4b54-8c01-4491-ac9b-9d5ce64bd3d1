package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CartitemMapper;
import com.ruoyi.system.domain.Cartitem;
import com.ruoyi.system.service.ICartitemService;

/**
 * 购物车商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class CartitemServiceImpl implements ICartitemService 
{
    @Autowired
    private CartitemMapper cartitemMapper;

    /**
     * 查询购物车商品
     * 
     * @param id 购物车商品主键
     * @return 购物车商品
     */
    @Override
    public Cartitem selectCartitemById(Long id)
    {
        return cartitemMapper.selectCartitemById(id);
    }

    /**
     * 查询购物车商品列表
     * 
     * @param cartitem 购物车商品
     * @return 购物车商品
     */
    @Override
    public List<Cartitem> selectCartitemList(Cartitem cartitem)
    {
        return cartitemMapper.selectCartitemList(cartitem);
    }

    /**
     * 新增购物车商品
     * 
     * @param cartitem 购物车商品
     * @return 结果
     */
    @Override
    public int insertCartitem(Cartitem cartitem)
    {
        return cartitemMapper.insertCartitem(cartitem);
    }

    /**
     * 修改购物车商品
     * 
     * @param cartitem 购物车商品
     * @return 结果
     */
    @Override
    public int updateCartitem(Cartitem cartitem)
    {
        return cartitemMapper.updateCartitem(cartitem);
    }

    /**
     * 批量删除购物车商品
     * 
     * @param ids 需要删除的购物车商品主键
     * @return 结果
     */
    @Override
    public int deleteCartitemByIds(Long[] ids)
    {
        return cartitemMapper.deleteCartitemByIds(ids);
    }

    /**
     * 删除购物车商品信息
     * 
     * @param id 购物车商品主键
     * @return 结果
     */
    @Override
    public int deleteCartitemById(Long id)
    {
        return cartitemMapper.deleteCartitemById(id);
    }
}
